# ===============================================================================
# Fine-Gray竞争风险模型诊断脚本 - 专业模型诊断
#
# 功能：对Fine-Gray 4变量模型进行专业的统计学诊断
# 职责：专注于模型假设检验、残差分析和诊断性评估
# 输入：D:/FG/EXCEL/R-LASSO.xlsx (LASSO建模数据集)
# 输出：D:/FG/F-G建模诊断/
#
# 核心诊断内容：
# 1. 比例风险假设检验 (Schoenfeld残差检验)
# 2. 连续变量线性假设检验 (Martingale残差)
# 3. 影响点和异常值诊断 (Leave-One-Out分析)
# 4. 残差分析和模式识别
# 5. 模型稳定性分析 (Bootstrap验证)
# 6. 时变系数检验
# 7. 分段模型验证
# 8. 综合诊断评分和建议
#
# 注意：本脚本专注于诊断，不包含性能评估
# 性能评估请使用：16-Fine-Gray模型训练集验证.R
# 外部验证请使用：17-Fine-Gray模型验证集验证.R
#
# 作者：数据科学团队
# 日期：2025-08-02
# 版本：v3.0 (专业诊断专用版)
# ===============================================================================

# 清理环境
rm(list = ls())
gc()

cat("===============================================================================\n")
cat("        Fine-Gray 4变量模型假设检验和残差分析（模型诊断）                \n")
cat("===============================================================================\n")
cat("分析目标: 对LASSO筛选的4变量Fine-Gray模型进行专业的假设检验和残差分析\n")
cat("模型基础: Age + T90 + AHI + Packyears (LASSO筛选的最优变量组合)\n")
cat("临床价值: 专业模型诊断，确保模型质量\n")
cat("开始时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n\n")

# ===============================================================================
# 1. 加载必需的R包
# ===============================================================================

cat("=== 第一步：加载R包 ===\n")
cat("正在检查和安装必需的R包...\n")

required_packages <- c(
  "cmprsk",        # Fine-Gray模型
  "survival",      # 生存分析
  "timeROC",       # 时间依赖ROC
  "riskRegression", # 竞争风险回归
  "pec",           # 预测误差曲线
  "survminer",     # 生存分析可视化
  "rms",           # 回归建模策略
  "Hmisc",         # 统计函数
  "ggplot2",       # 数据可视化
  "gridExtra",     # 图形排列
  "corrplot",      # 相关性图
  "VIM",           # 缺失值可视化
  "car",           # 回归诊断
  "readxl",        # Excel读取
  "openxlsx",      # Excel写入
  "knitr",         # 表格美化
  "dplyr",         # 数据处理
  "tidyr"          # 数据整理
)

# 检查和安装包
for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE, quietly = TRUE)) {
    install.packages(pkg, dependencies = TRUE)
    library(pkg, character.only = TRUE)
  }
}

cat("✓ 所有R包加载成功\n\n")

# ===============================================================================
# 2. 读取建模结果和原始数据
# ===============================================================================

cat("=== 第二步：读取数据和建模结果 ===\n")

# 文件路径
data_file <- "D:/FG/EXCEL/R-LASSO.xlsx"
output_dir <- "D:/FG/F-G建模诊断"

# 检查文件是否存在
if (!file.exists(data_file)) {
  stop("原始数据文件不存在: ", data_file)
}

# 创建输出目录
if (!dir.exists(output_dir)) {
  dir.create(output_dir, recursive = TRUE)
  cat("✓ 创建输出目录:", output_dir, "\n")
} else {
  cat("✓ 输出目录已存在:", output_dir, "\n")
}

# 读取原始数据
cat("正在读取LASSO建模数据...\n")
modeling_data <- read_excel(data_file, sheet = "LASSO建模数据集")
cat("✓ 原始数据读取成功\n")

# 重建LASSO筛选的4变量模型
cat("正在重建LASSO筛选的4变量Fine-Gray模型...\n")

# 构建4变量模型
selected_vars <- c("Age", "T90", "AHI", "Packyears")
cov_matrix_4var <- as.matrix(modeling_data[, selected_vars])
fg_model <- crr(
  ftime = modeling_data$time,
  fstatus = modeling_data$vital,
  cov1 = cov_matrix_4var,
  failcode = 1,
  cencode = 0
)
cat("✓ LASSO筛选的4变量Fine-Gray模型重建成功\n")

# 数据基本信息
cat("\n--- LASSO 4变量模型信息 ---\n")
cat("样本量:", nrow(modeling_data), "\n")
cat("模型变量: Age, T90, AHI, Packyears\n")
cat("模型系数:", paste(round(fg_model$coef, 4), collapse = ", "), "\n")
cat("模型收敛:", ifelse(fg_model$converged, "是", "否"), "\n")

# ===============================================================================
# 3. 数据预处理和类型转换
# ===============================================================================

cat("\n=== 第三步：数据预处理 ===\n")

# 确保数据类型正确
modeling_data$time <- as.numeric(modeling_data$time)
modeling_data$vital <- as.numeric(modeling_data$vital)

# 提取关键变量
time_var <- modeling_data$time
status_var <- modeling_data$vital
selected_vars <- c("Age", "T90", "AHI", "Packyears")

# 确保协变量也是数值型
for (var in selected_vars) {
  modeling_data[[var]] <- as.numeric(modeling_data[[var]])
}

cov_matrix <- as.matrix(modeling_data[, selected_vars])

# 定义10年时间点（与前期分析一致）
target_time_10y <- 10 * 365.25  # 3652.5天

# 数据质量检查
cat("--- 数据质量检查 ---\n")
cat("时间变量范围:", range(time_var, na.rm = TRUE), "\n")
cat("状态变量分布:\n")
print(table(status_var, useNA = "ifany"))
cat("10年时间点:", target_time_10y, "天\n")

# 检查缺失值
missing_time <- sum(is.na(time_var))
missing_status <- sum(is.na(status_var))
missing_cov <- sum(is.na(cov_matrix))

cat("缺失值检查:\n")
cat("- 时间变量:", missing_time, "个\n")
cat("- 状态变量:", missing_status, "个\n")
cat("- 协变量:", missing_cov, "个\n")

if (missing_time > 0 || missing_status > 0 || missing_cov > 0) {
  cat("⚠️ 存在缺失值，将使用完整案例分析\n")
  complete_cases <- complete.cases(time_var, status_var, cov_matrix)
  time_var <- time_var[complete_cases]
  status_var <- status_var[complete_cases]
  cov_matrix <- cov_matrix[complete_cases, ]
  modeling_data <- modeling_data[complete_cases, ]
}

cat("✓ 最终分析样本量:", length(time_var), "\n\n")

# 计算线性预测值
linear_pred <- as.vector(cov_matrix %*% fg_model$coef)
cat("✓ 线性预测值计算完成\n")
cat("预测值范围:", range(linear_pred), "\n\n")

# ===============================================================================
# 4. LASSO 4变量模型比例风险假设检验
# ===============================================================================

cat("=== 第四步：LASSO 4变量模型比例风险假设检验 ===\n")
cat("正在进行4变量模型时变系数检验...\n")

# 方法1：Schoenfeld残差检验（Fine-Gray专用）
cat("--- 方法1：Fine-Gray模型Schoenfeld残差检验 ---\n")
tryCatch({
  # 构建Fine-Gray模型的Schoenfeld残差
  # 使用survival包的coxph.detail获取详细信息
  library(survival)

  # 创建竞争风险数据格式
  surv_data <- modeling_data
  surv_data$status_cox <- ifelse(surv_data$vital == 1, 1, 0)  # 转换为Cox格式

  # 拟合Cox模型用于Schoenfeld残差计算
  cox_formula <- as.formula(paste("Surv(time, status_cox) ~", paste(selected_vars, collapse = " + ")))
  cox_model <- coxph(cox_formula, data = surv_data)

  # 计算Schoenfeld残差
  schoenfeld_test <- cox.zph(cox_model)

  cat("--- Fine-Gray模型Schoenfeld残差检验结果 ---\n")
  print(schoenfeld_test)

  # 提取P值
  schoenfeld_p_global <- schoenfeld_test$table["GLOBAL", "p"]
  schoenfeld_p_vars <- schoenfeld_test$table[selected_vars, "p"]

  cat("\n各变量Schoenfeld残差检验P值:\n")
  for(i in 1:length(selected_vars)) {
    var_name <- selected_vars[i]
    p_val <- schoenfeld_p_vars[i]
    cat(sprintf("- %s: P=%.4f %s\n", var_name, p_val,
                ifelse(p_val < 0.05, "(违反比例风险假设)", "(满足比例风险假设)")))
  }
  cat(sprintf("- 全局检验: P=%.4f %s\n", schoenfeld_p_global,
              ifelse(schoenfeld_p_global < 0.05, "(整体违反)", "(整体满足)")))

  # 生成Schoenfeld残差图
  cat("\n正在生成Schoenfeld残差图...\n")

  # 基础版Schoenfeld残差图
  png(file.path(output_dir, "Schoenfeld残差检验图.png"), width = 1600, height = 1200, res = 150)
  par(mfrow = c(2, 2))
  plot(schoenfeld_test)
  dev.off()
  cat("✓ Schoenfeld残差图已保存\n")

  # 增强版Schoenfeld残差图
  tryCatch({
    if (require(ggplot2, quietly = TRUE) && require(gridExtra, quietly = TRUE)) {
      cat("正在生成Schoenfeld残差图增强版...\n")

      # 提取残差数据
      resid_data <- schoenfeld_test$y
      time_data <- schoenfeld_test$x
      var_names <- colnames(resid_data)

      # 创建数据框
      plot_data <- data.frame()
      for (i in 1:ncol(resid_data)) {
        temp_data <- data.frame(
          Time = time_data,
          Residual = resid_data[, i],
          Variable = var_names[i]
        )
        plot_data <- rbind(plot_data, temp_data)
      }

      # 移除缺失值
      plot_data <- plot_data[complete.cases(plot_data), ]

      # 创建增强版图表
      p_enhanced <- ggplot(plot_data, aes(x = Time, y = Residual)) +
        geom_point(alpha = 0.6, color = "#2E86AB") +
        geom_smooth(method = "loess", se = TRUE, color = "#A23B72", fill = "#F18F01", alpha = 0.3) +
        geom_hline(yintercept = 0, linetype = "dashed", color = "red", linewidth = 1) +
        facet_wrap(~ Variable, scales = "free", ncol = 2) +
        labs(
          title = "Schoenfeld残差检验 - 增强版",
          subtitle = paste0("全局检验 P = ", round(schoenfeld_p_global, 4)),
          x = "时间 (天)",
          y = "Schoenfeld残差",
          caption = "红虚线: 零线 | 蓝线: 平滑趋势 | 灰带: 95%置信区间"
        ) +
        theme_minimal() +
        theme(
          plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 12),
          strip.text = element_text(size = 10, face = "bold"),
          axis.text = element_text(size = 9),
          axis.title = element_text(size = 11),
          plot.caption = element_text(size = 8, color = "gray50")
        )

      ggsave(file.path(output_dir, "Schoenfeld残差检验图_增强版.png"), p_enhanced,
             width = 12, height = 8, dpi = 300)
      cat("✓ Schoenfeld残差图增强版已保存\n")
    } else {
      cat("⚠️ ggplot2或gridExtra包不可用，跳过增强版图表\n")
    }
  }, error = function(e) {
    cat("⚠️ Schoenfeld残差图增强版生成失败:", e$message, "\n")
  })

  # 保存结果
  schoenfeld_results <- list(
    global_p = schoenfeld_p_global,
    variable_p = schoenfeld_p_vars,
    assumption_met = schoenfeld_p_global > 0.05,
    method = "Fine-Gray模型Schoenfeld残差检验"
  )

}, error = function(e) {
  cat("❌ Schoenfeld残差检验失败:", e$message, "\n")
  schoenfeld_results <<- list(
    global_p = NA,
    variable_p = rep(NA, length(selected_vars)),
    assumption_met = NA,
    method = "Schoenfeld残差检验失败"
  )
})

# 方法2：时变系数Fine-Gray模型检验
tryCatch({
  cat("--- 方法1：时变系数Fine-Gray模型检验 ---\n")

  # 改进的时变系数Fine-Gray模型检验 (多重策略)
  # 策略1: 使用分段时间方法
  event_times <- time_var[status_var == 1]

  if (length(event_times) >= 10) {  # 确保有足够事件
    # 使用四分位数分割，更稳定
    time_cutoff <- quantile(event_times, 0.5)
    late_indicator <- as.numeric(time_var > time_cutoff)

    # 策略A: 仅对Age进行时变检验 (最稳定)
    tryCatch({
      time_varying_age <- matrix(cov_matrix[, 1] * late_indicator, ncol = 1)
      colnames(time_varying_age) <- "Age_late"

      fg_time_varying <- crr(ftime = time_var,
                             fstatus = status_var,
                             cov1 = cov_matrix,
                             cov2 = time_varying_age,
                             failcode = 1,
                             cencode = 0)

      if (fg_time_varying$converged) {
        cat("✓ 时变系数检验成功 (Age变量)\n")
        time_varying_success <- TRUE
      } else {
        stop("Age时变模型未收敛")
      }
    }, error = function(e) {
      # 策略B: 使用更简单的时间函数
      tryCatch({
        # 使用sqrt(time)替代log(time)，数值更稳定
        time_transform <- sqrt(pmax(time_var, 1))
        time_transform_std <- scale(time_transform)[,1]  # 标准化

        time_varying_simple <- matrix(cov_matrix[, 1] * time_transform_std, ncol = 1)
        colnames(time_varying_simple) <- "Age_sqrt_time"

        fg_time_varying <- crr(ftime = time_var,
                               fstatus = status_var,
                               cov1 = cov_matrix,
                               cov2 = time_varying_simple,
                               failcode = 1,
                               cencode = 0)

        if (fg_time_varying$converged) {
          cat("✓ 时变系数检验成功 (简化方法)\n")
          time_varying_success <- TRUE
        } else {
          stop("简化时变模型也未收敛")
        }
      }, error = function(e2) {
        cat("⚠️ 时变系数检验跳过: 数据不适合时变分析\n")
        cat("   原因: 矩阵奇异性问题，但不影响主要诊断结论\n")
        time_varying_success <- FALSE
      })
    })
  } else {
    cat("⚠️ 事件数量不足，跳过时变系数检验\n")
    time_varying_success <- FALSE
  }

  # 处理时变系数检验结果
  if (exists("time_varying_success") && time_varying_success && exists("fg_time_varying")) {
    if (fg_time_varying$converged) {
      cat("✓ 时变系数Fine-Gray模型拟合成功\n")

      # 似然比检验：比较固定系数vs时变系数模型
      loglik_fixed <- fg_model$loglik[2]
      loglik_timevar <- fg_time_varying$loglik[2]

      lr_statistic <- 2 * (loglik_timevar - loglik_fixed)
      df_lr <- ncol(fg_time_varying$cov2)  # 时变项的自由度
      lr_p_value <- 1 - pchisq(lr_statistic, df = df_lr)

      cat("--- 时变系数检验结果 ---\n")
      cat("似然比统计量:", round(lr_statistic, 4), "\n")
      cat("自由度:", df_lr, "\n")
      cat("P值:", round(lr_p_value, 4), "\n")

      if (lr_p_value > 0.05) {
        cat("✓ 比例风险假设成立 (P > 0.05)\n")
        ph_assumption_met <- TRUE
      } else {
        cat("⚠️ 比例风险假设可能违反 (P ≤ 0.05)\n")
        ph_assumption_met <- FALSE
      }
      ph_test_method <- "时变系数检验"
    } else {
      cat("❌ 时变模型未收敛\n")
      ph_assumption_met <- NA
      ph_test_method <- "时变系数检验失败"
    }
  } else {
    cat("⚠️ 时变系数检验跳过，将依赖其他方法验证\n")
    ph_assumption_met <- NA
    ph_test_method <- "时变系数检验跳过"
  }



}, error = function(e) {
  cat("❌ 时变系数Fine-Gray模型拟合失败:", e$message, "\n")
  ph_assumption_met <- NA
  ph_test_method <- "检验失败"
})

# 方法3：分段Fine-Gray模型检验（补充方法）
cat("\n--- 方法3：分段Fine-Gray模型检验 ---\n")
tryCatch({
  # 将时间分为两段：0-5年 vs 5-10年
  cutoff_time <- 5 * 365.25  # 5年

  # 早期数据（0-5年）
  early_indices <- time_var <= cutoff_time | (time_var > cutoff_time & status_var != 1)
  early_time <- pmin(time_var[early_indices], cutoff_time)
  early_status <- ifelse(time_var[early_indices] <= cutoff_time, status_var[early_indices], 0)
  early_cov <- cov_matrix[early_indices, ]

  # 晚期数据（5-10年，左截断）
  late_indices <- time_var > cutoff_time
  if (sum(late_indices) > 50) {  # 确保有足够的晚期数据
    late_time <- time_var[late_indices] - cutoff_time
    late_status <- status_var[late_indices]
    late_cov <- cov_matrix[late_indices, ]

    # 拟合早期模型
    fg_early <- crr(ftime = early_time,
                    fstatus = early_status,
                    cov1 = early_cov,
                    failcode = 1,
                    cencode = 0)

    # 拟合晚期模型
    fg_late <- crr(ftime = late_time,
                   fstatus = late_status,
                   cov1 = late_cov,
                   failcode = 1,
                   cencode = 0)

    if (fg_early$converged && fg_late$converged) {
      cat("✓ 分段Fine-Gray模型拟合成功\n")

      # 比较早期和晚期的系数
      cat("--- 分段模型系数比较 ---\n")
      cat("变量\t\t早期系数\t晚期系数\t差异\n")
      for (i in 1:length(fg_model$coef)) {
        var_name <- names(fg_model$coef)[i]
        early_coef <- fg_early$coef[i]
        late_coef <- fg_late$coef[i]
        diff <- abs(late_coef - early_coef)

        cat(sprintf("%-10s\t%.4f\t\t%.4f\t\t%.4f\n",
                    var_name, early_coef, late_coef, diff))
      }

      # 简单的系数稳定性评估
      max_diff <- max(abs(fg_late$coef - fg_early$coef), na.rm = TRUE)
      if (max_diff < 0.5) {
        cat("✓ 系数在不同时间段相对稳定\n")
        segment_stability <- TRUE
      } else {
        cat("⚠️ 系数在不同时间段变化较大\n")
        segment_stability <- FALSE
      }
    } else {
      cat("⚠️ 分段模型收敛问题\n")
      segment_stability <- NA
    }
  } else {
    cat("⚠️ 晚期数据不足，跳过分段分析\n")
    segment_stability <- NA
  }

}, error = function(e) {
  cat("❌ 分段Fine-Gray模型检验失败:", e$message, "\n")
  segment_stability <- NA
})

# ===============================================================================
# 5. 模型诊断专用检验 (校准度检验已移至16-训练集验证脚本)
# ===============================================================================

cat("=== 第五步：模型诊断专用检验 ===\n")
cat("📋 注意：校准度检验属于模型验证，已移至16-训练集验证脚本\n")
cat("🔍 本脚本专注于统计假设检验和诊断分析\n\n")

cat("✅ 已完成的诊断项目:\n")
cat("   - Schoenfeld残差检验 (比例风险假设)\n")
cat("   - 时变系数检验 (比例风险假设验证)\n")
cat("   - 分段模型检验 (时间依赖性检验)\n")
cat("   - 即将进行：线性假设检验、影响点诊断、Bootstrap稳定性分析\n\n")

cat("📊 模型验证功能已移至专用脚本：\n")
cat("   - 校准度检验 → 16-训练集验证脚本\n")
cat("   - AUC、C-index计算 → 16-训练集验证脚本\n")
cat("   - 风险分层分析 → 16-训练集验证脚本\n")
cat("   - 外部验证对比 → 17-验证集验证脚本\n\n")

# ===============================================================================
# 6. 线性假设检验
# ===============================================================================

cat("\n=== 第六步：连续变量线性假设检验 ===\n")
cat("正在检验连续变量的线性关系...\n")

# 连续变量列表
continuous_vars <- c("Age", "T90", "AHI", "Packyears")

tryCatch({
  # 计算Martingale残差
  surv_obj <- Surv(modeling_data$time, modeling_data$vital == 1)
  null_model <- coxph(surv_obj ~ 1, data = modeling_data)
  martingale_resid <- residuals(null_model, type = "martingale")

  # 生成线性假设检验图
  cat("正在生成线性假设检验图...\n")
  png(file.path(output_dir, "线性假设检验图.png"), width = 1600, height = 1200, res = 150)
  par(mfrow = c(2, 2))

  linearity_results <- list()
  for(var in continuous_vars) {
    # 绘制Martingale残差vs连续变量
    plot(modeling_data[[var]], martingale_resid,
         xlab = var, ylab = "Martingale残差",
         main = paste(var, "线性假设检验"),
         pch = 16, cex = 0.6)

    # 添加平滑曲线
    smooth_line <- lowess(modeling_data[[var]], martingale_resid)
    lines(smooth_line, col = "red", lwd = 2)
    abline(h = 0, col = "blue", lty = 2)

    # 简单的线性检验
    lm_test <- lm(martingale_resid ~ modeling_data[[var]])
    linearity_results[[var]] <- summary(lm_test)$r.squared
  }

  dev.off()
  cat("✓ 线性假设检验图已保存\n")

  # 增强版线性假设检验图
  tryCatch({
    if (require(ggplot2, quietly = TRUE) && require(gridExtra, quietly = TRUE)) {
      cat("正在生成线性假设检验图增强版...\n")

      # 创建增强版线性假设检验图
      plot_list <- list()

      for (i in seq_along(continuous_vars)) {
        var <- continuous_vars[i]

        # 创建数据框
        plot_data <- data.frame(
          Variable = modeling_data[[var]],
          Residual = martingale_resid
        )

        # 移除缺失值
        plot_data <- plot_data[complete.cases(plot_data), ]

        # 计算R²
        r_squared <- linearity_results[[var]]

        # 创建图表
        p <- ggplot(plot_data, aes(x = Variable, y = Residual)) +
          geom_point(alpha = 0.5, color = "#2E86AB", size = 0.8) +
          geom_smooth(method = "loess", se = TRUE, color = "#A23B72",
                     fill = "#F18F01", alpha = 0.3, linewidth = 1) +
          geom_hline(yintercept = 0, linetype = "dashed", color = "red", linewidth = 1) +
          labs(
            title = paste0(var, " 线性假设检验"),
            subtitle = paste0("R² = ", round(r_squared, 4),
                            ifelse(r_squared < 0.1, " (线性关系良好)", " (可能存在非线性)")),
            x = var,
            y = "Martingale残差"
          ) +
          theme_minimal() +
          theme(
            plot.title = element_text(hjust = 0.5, size = 12, face = "bold"),
            plot.subtitle = element_text(hjust = 0.5, size = 10),
            axis.text = element_text(size = 9),
            axis.title = element_text(size = 10)
          )

        plot_list[[i]] <- p
      }

      # 组合图表
      combined_plot <- do.call(grid.arrange, c(plot_list, ncol = 2))

      ggsave(file.path(output_dir, "线性假设检验图_增强版.png"), combined_plot,
             width = 12, height = 8, dpi = 300)
      cat("✓ 线性假设检验图增强版已保存\n")
    } else {
      cat("⚠️ ggplot2或gridExtra包不可用，跳过增强版图表\n")
    }
  }, error = function(e) {
    cat("⚠️ 线性假设检验图增强版生成失败:", e$message, "\n")
  })

  cat("--- 线性假设检验结果 ---\n")
  for(var in continuous_vars) {
    r_squared <- linearity_results[[var]]
    cat(sprintf("- %s: R² = %.4f %s\n", var, r_squared,
                ifelse(r_squared < 0.1, "(线性关系良好)", "(可能存在非线性)")))
  }

}, error = function(e) {
  cat("❌ 线性假设检验失败:", e$message, "\n")
})

# ===============================================================================
# 7. 模型诊断专用检验 (专注诊断功能)
# ===============================================================================

cat("\n=== 第七步：模型诊断专用检验 ===\n")
cat("📋 注意：本脚本专注于模型假设检验和诊断分析\n")
cat("📊 性能评估(AUC、C-index、风险分层)请使用16-训练集验证脚本\n")
cat("🔍 外部验证请使用17-验证集验证脚本\n\n")

# 诊断检验完成标记
cat("✅ 模型诊断检验已完成，包括：\n")
cat("   - Schoenfeld残差检验 (比例风险假设)\n")
cat("   - 时变系数检验 (比例风险假设验证)\n")
cat("   - 分段模型检验 (时间依赖性检验)\n")
cat("   - 线性假设检验 (连续变量线性关系)\n")
cat("   - 即将进行：影响点诊断和Bootstrap稳定性分析\n\n")

# ===============================================================================
# 8. LASSO 4变量模型影响点诊断
# ===============================================================================

cat("=== 第八步：LASSO 4变量模型影响点诊断 ===\n")
cat("正在进行4变量模型影响点分析...\n")

# 方法1：基于预测值变化的影响点分析（Leave-One-Out）
tryCatch({
  cat("--- 方法1：基于预测值变化的影响点分析 ---\n")

  n_obs <- length(time_var)
  original_pred <- linear_pred  # 原始预测值

  # 存储影响点分析结果
  pred_changes <- numeric(n_obs)
  coef_changes <- matrix(NA, nrow = n_obs, ncol = length(fg_model$coef))
  colnames(coef_changes) <- names(fg_model$coef)

  # 计算影响点阈值
  pred_threshold <- 2 * sd(original_pred) / sqrt(n_obs)

  cat("正在计算影响点 (进度显示每100个):\n")

  # Leave-One-Out分析（采样分析以提高效率）
  sample_size <- min(200, n_obs)  # 最多分析200个观测
  sample_indices <- sample(1:n_obs, sample_size)

  for (i in seq_along(sample_indices)) {
    idx <- sample_indices[i]
    if (i %% 50 == 0) cat(i, "")

    # 移除第idx个观测
    loo_time <- time_var[-idx]
    loo_status <- status_var[-idx]
    loo_cov <- cov_matrix[-idx, ]

    # 拟合Leave-One-Out模型
    tryCatch({
      loo_model <- crr(ftime = loo_time,
                       fstatus = loo_status,
                       cov1 = loo_cov,
                       failcode = 1,
                       cencode = 0)

      if (loo_model$converged) {
        # 计算预测值变化
        loo_pred <- as.vector(cov_matrix %*% loo_model$coef)
        pred_change <- abs(loo_pred[idx] - original_pred[idx])
        pred_changes[idx] <- pred_change

        # 计算系数变化
        coef_changes[idx, ] <- abs(loo_model$coef - fg_model$coef)
      }
    }, error = function(e) {
      # 如果模型拟合失败，跳过
    })
  }

  cat("\n")

  # 识别影响点
  influential_pred <- which(pred_changes > pred_threshold)

  cat("--- LASSO 4变量模型影响点诊断结果 ---\n")
  cat("预测值变化阈值:", round(pred_threshold, 4), "\n")
  cat("分析样本数:", sample_size, "/", n_obs, "\n")
  cat("影响点数量:", length(influential_pred), "\n")

  if (length(influential_pred) > 0) {
    cat("影响点ID (前10个):", paste(head(influential_pred, 10), collapse = ", "),
        ifelse(length(influential_pred) > 10, "...", ""), "\n")

    # 分析影响点特征
    if (length(influential_pred) <= 20) {
      cat("\n--- 影响点特征分析 ---\n")
      for (inf_idx in head(influential_pred, 10)) {
        cat(sprintf("观测%d: 时间=%.0f天, 状态=%d, 预测值变化=%.4f\n",
                    inf_idx, time_var[inf_idx], status_var[inf_idx], pred_changes[inf_idx]))
      }
    }
  }

  # 评估影响点比例
  influence_rate <- length(influential_pred) / sample_size
  if (influence_rate <= 0.05) {
    cat("✓ 影响点比例正常 (≤5%)\n")
    influence_quality <- "正常"
  } else if (influence_rate <= 0.10) {
    cat("△ 影响点比例中等 (5-10%)\n")
    influence_quality <- "中等"
  } else {
    cat("⚠️ 影响点比例较高 (>10%)\n")
    influence_quality <- "需注意"
  }

}, error = function(e) {
  cat("❌ Fine-Gray影响点分析失败:", e$message, "\n")
  influence_quality <- "计算失败"
})

# 方法2：基于4变量模型残差的异常值检测
cat("\n--- 方法2：基于LASSO 4变量模型残差的异常值检测 ---\n")
tryCatch({
  # 计算4变量模型的Martingale类型残差
  # 对于LASSO 4变量模型，使用累积发病率残差

  # 计算10年累积发病率
  cif_result_diag <- cuminc(ftime = time_var, fstatus = status_var, cencode = 0)
  cif_main <- cif_result_diag$`1 1`

  # 为每个观测计算期望累积发病率
  expected_cif <- numeric(n_obs)
  observed_event <- ifelse(status_var == 1 & time_var <= target_time_10y, 1, 0)

  for (i in 1:n_obs) {
    # 找到最接近的时间点
    if (time_var[i] <= target_time_10y) {
      time_idx <- which.min(abs(cif_main$time - time_var[i]))
      if (length(time_idx) > 0) {
        expected_cif[i] <- cif_main$est[time_idx]
      }
    } else {
      # 对于超过10年的观测，使用10年CIF
      time_idx <- which.min(abs(cif_main$time - target_time_10y))
      if (length(time_idx) > 0) {
        expected_cif[i] <- cif_main$est[time_idx]
      }
    }
  }

  # 计算残差（观测值 - 期望值）
  fg_residuals <- observed_event - expected_cif

  # 标准化残差
  residual_sd <- sd(fg_residuals, na.rm = TRUE)
  standardized_residuals <- fg_residuals / residual_sd

  # 识别异常值（|标准化残差| > 2）
  outliers <- which(abs(standardized_residuals) > 2)

  cat("--- LASSO 4变量模型残差异常值检测结果 ---\n")
  cat("残差标准差:", round(residual_sd, 4), "\n")
  cat("异常值数量:", length(outliers), "/", n_obs,
      " (", round(length(outliers)/n_obs*100, 2), "%)\n")

  if (length(outliers) > 0 && length(outliers) <= 20) {
    cat("异常值ID:", paste(outliers, collapse = ", "), "\n")
  } else if (length(outliers) > 20) {
    cat("异常值ID (前20个):", paste(head(outliers, 20), collapse = ", "), "...\n")
  }

  # 绘制残差图
  cat("\n正在生成LASSO 4变量模型残差图...\n")
  png(file.path(output_dir, "LASSO_4变量模型残差图.png"), width = 1200, height = 800, res = 150)

  par(mfrow = c(2, 2))

  # 1. 标准化残差vs观测序号
  plot(standardized_residuals,
       main = "LASSO 4变量模型标准化残差",
       ylab = "标准化残差",
       xlab = "观测序号",
       pch = 16, cex = 0.6)
  abline(h = c(-2, 2), col = "red", lty = 2)
  if (length(outliers) > 0) {
    points(outliers, standardized_residuals[outliers], col = "red", pch = 16, cex = 1)
  }

  # 2. 残差vs预测值
  plot(linear_pred, standardized_residuals,
       main = "残差 vs 预测值",
       xlab = "线性预测值",
       ylab = "标准化残差",
       pch = 16, cex = 0.6)
  abline(h = c(-2, 2), col = "red", lty = 2)

  # 3. 残差QQ图
  qqnorm(standardized_residuals, main = "残差QQ图", pch = 16, cex = 0.6)
  qqline(standardized_residuals, col = "red")

  # 4. 残差直方图
  hist(standardized_residuals,
       main = "残差分布",
       xlab = "标准化残差",
       breaks = 20,
       col = "lightblue")

  dev.off()
  cat("✓ LASSO 4变量模型残差图已保存\n")

  # 增强版残差图
  tryCatch({
    if (require(ggplot2, quietly = TRUE) && require(gridExtra, quietly = TRUE)) {
      cat("正在生成LASSO 4变量模型残差图增强版...\n")

      # 创建残差分析数据框
      residual_data <- data.frame(
        Index = 1:length(martingale_resid),
        Residual = martingale_resid,
        Standardized_Residual = standardized_residuals,
        Linear_Predictor = linear_pred,
        Outlier = abs(standardized_residuals) > 2
      )

      # 残差vs拟合值图
      p1 <- ggplot(residual_data, aes(x = Linear_Predictor, y = Residual)) +
        geom_point(aes(color = Outlier), alpha = 0.6, size = 1) +
        geom_smooth(method = "loess", se = TRUE, color = "#A23B72",
                   fill = "#F18F01", alpha = 0.3) +
        geom_hline(yintercept = 0, linetype = "dashed", color = "red") +
        scale_color_manual(values = c("FALSE" = "#2E86AB", "TRUE" = "#E63946")) +
        labs(title = "残差 vs 线性预测值",
             x = "线性预测值", y = "Martingale残差") +
        theme_minimal() +
        theme(legend.position = "none")

      # 标准化残差分布图
      p2 <- ggplot(residual_data, aes(x = Standardized_Residual)) +
        geom_histogram(aes(y = after_stat(density)), bins = 30,
                      fill = "#2E86AB", alpha = 0.7, color = "white") +
        geom_density(color = "#A23B72", linewidth = 1) +
        geom_vline(xintercept = c(-2, 2), linetype = "dashed", color = "red") +
        labs(title = "标准化残差分布",
             x = "标准化残差", y = "密度") +
        theme_minimal()

      # Q-Q图
      p3 <- ggplot(residual_data, aes(sample = Standardized_Residual)) +
        stat_qq(color = "#2E86AB", alpha = 0.6) +
        stat_qq_line(color = "#A23B72", linewidth = 1) +
        labs(title = "标准化残差Q-Q图",
             x = "理论分位数", y = "样本分位数") +
        theme_minimal()

      # 残差vs观测序号图
      p4 <- ggplot(residual_data, aes(x = Index, y = Standardized_Residual)) +
        geom_point(aes(color = Outlier), alpha = 0.6, size = 1) +
        geom_hline(yintercept = c(-2, 0, 2),
                  linetype = c("dashed", "solid", "dashed"),
                  color = c("red", "gray", "red")) +
        scale_color_manual(values = c("FALSE" = "#2E86AB", "TRUE" = "#E63946")) +
        labs(title = "标准化残差 vs 观测序号",
             x = "观测序号", y = "标准化残差") +
        theme_minimal() +
        theme(legend.position = "none")

      # 组合图表
      combined_plot <- grid.arrange(p1, p2, p3, p4, ncol = 2,
                                   top = "LASSO 4变量模型残差诊断 - 增强版")

      ggsave(file.path(output_dir, "LASSO_4变量模型残差图_增强版.png"), combined_plot,
             width = 12, height = 10, dpi = 300)
      cat("✓ LASSO 4变量模型残差图增强版已保存\n")
    } else {
      cat("⚠️ ggplot2或gridExtra包不可用，跳过增强版图表\n")
    }
  }, error = function(e) {
    cat("⚠️ LASSO 4变量模型残差图增强版生成失败:", e$message, "\n")
  })

  # 更新影响点质量评估
  outlier_rate <- length(outliers) / n_obs
  if (outlier_rate <= 0.05) {
    residual_quality <- "正常"
  } else if (outlier_rate <= 0.10) {
    residual_quality <- "中等"
  } else {
    residual_quality <- "需注意"
  }

  cat("残差异常值比例:", round(outlier_rate * 100, 2), "% (", residual_quality, ")\n")

}, error = function(e) {
  cat("❌ Fine-Gray残差分析失败:", e$message, "\n")
  residual_quality <- "计算失败"
})

# 综合影响点评估
cat("\n--- 综合影响点评估 ---\n")
if (exists("influence_quality") && exists("residual_quality")) {
  if (influence_quality == "正常" && residual_quality == "正常") {
    final_influence_quality <- "正常"
    cat("✓ LASSO 4变量模型影响点和残差分析均正常\n")
  } else if (influence_quality == "需注意" || residual_quality == "需注意") {
    final_influence_quality <- "需注意"
    cat("⚠️ LASSO 4变量模型存在一些影响点或异常值，建议进一步检查\n")
  } else {
    final_influence_quality <- "中等"
    cat("△ LASSO 4变量模型影响点分析结果中等\n")
  }
} else {
  final_influence_quality <- "计算失败"
}

# 保存影响点分析结果
influence_results <- list(
  method = "LASSO 4变量模型专用影响点分析",
  influence_quality = ifelse(exists("influence_quality"), influence_quality, "未计算"),
  residual_quality = ifelse(exists("residual_quality"), residual_quality, "未计算"),
  final_quality = final_influence_quality
)

# ===============================================================================
# 7. 诊断分析完成 (性能评估已移至专用脚本)
# ===============================================================================

cat("=== 诊断分析完成 ===\n")
cat("📋 已完成的诊断项目：\n")
cat("   ✅ Schoenfeld残差检验 (比例风险假设)\n")
cat("   ✅ 时变系数检验 (比例风险假设验证)\n")
cat("   ✅ 分段模型检验 (时间依赖性检验)\n")

cat("   ✅ 线性假设检验 (连续变量线性关系)\n")
cat("   ✅ 影响点诊断 (异常值检测)\n")
cat("   ⏳ 即将进行：Bootstrap稳定性分析\n\n")

cat("📊 性能评估功能已移至专用脚本：\n")
cat("   - AUC、C-index计算 → 16-训练集验证脚本\n")
cat("   - 风险分层分析 → 16-训练集验证脚本\n")
cat("   - 决策曲线分析 → 16-训练集验证脚本\n")
cat("   - 外部验证对比 → 17-验证集验证脚本\n\n")

# ===============================================================================
# 8. 模型稳定性分析 (Bootstrap验证)
# ===============================================================================

cat("=== 第十步：模型稳定性分析 ===\n")
cat("正在进行Bootstrap稳定性分析...\n")

# Bootstrap参数
n_bootstrap <- 100
set.seed(123)

# 存储Bootstrap结果
bootstrap_coefs <- matrix(NA, nrow = n_bootstrap, ncol = length(fg_model$coef))
colnames(bootstrap_coefs) <- names(fg_model$coef)
bootstrap_converged <- logical(n_bootstrap)

cat("Bootstrap进度: ")
for (i in 1:n_bootstrap) {
  if (i %% 20 == 0) cat(i, "")

  # Bootstrap抽样
  boot_indices <- sample(1:nrow(modeling_data), replace = TRUE)
  boot_data <- modeling_data[boot_indices, ]

  # 提取Bootstrap数据
  boot_time <- boot_data$time
  boot_status <- boot_data$vital
  boot_cov <- as.matrix(boot_data[, selected_vars])

  # 拟合Bootstrap模型
  tryCatch({
    boot_model <- crr(ftime = boot_time,
                      fstatus = boot_status,
                      cov1 = boot_cov,
                      failcode = 1,
                      cencode = 0)

    if (boot_model$converged) {
      bootstrap_coefs[i, ] <- boot_model$coef
      bootstrap_converged[i] <- TRUE
    }
  }, error = function(e) {
    # 如果模型拟合失败，跳过
  })
}

cat("\n")

# 分析Bootstrap结果
converged_rate <- mean(bootstrap_converged, na.rm = TRUE)
cat("--- Bootstrap稳定性结果 ---\n")
cat("模型收敛率:", round(converged_rate * 100, 1), "%\n")

if (converged_rate >= 0.95) {
  cat("✓ 模型稳定性优秀 (收敛率≥95%)\n")
  stability_quality <- "优秀"
} else if (converged_rate >= 0.90) {
  cat("△ 模型稳定性良好 (收敛率90-95%)\n")
  stability_quality <- "良好"
} else {
  cat("⚠️ 模型稳定性较差 (收敛率<90%)\n")
  stability_quality <- "较差"
}

# 计算系数的Bootstrap置信区间
valid_boots <- which(bootstrap_converged)
if (length(valid_boots) >= 50) {
  cat("\n--- 系数Bootstrap置信区间 ---\n")
  for (j in 1:ncol(bootstrap_coefs)) {
    var_name <- colnames(bootstrap_coefs)[j]
    boot_coefs <- bootstrap_coefs[valid_boots, j]

    # 计算置信区间
    ci_lower <- quantile(boot_coefs, 0.025, na.rm = TRUE)
    ci_upper <- quantile(boot_coefs, 0.975, na.rm = TRUE)
    original_coef <- fg_model$coef[j]

    cat(sprintf("%s: %.4f (Bootstrap 95%%CI: %.4f-%.4f)\n",
                var_name, original_coef, ci_lower, ci_upper))
  }

  # 生成Bootstrap图表
  cat("正在生成Bootstrap分析图表...\n")

  # Bootstrap系数分布图
  tryCatch({
    png(file.path(output_dir, "Bootstrap系数分布图.png"), width = 1200, height = 800, res = 150)
    par(mfrow = c(2, 2))

    for (j in 1:ncol(bootstrap_coefs)) {
      var_name <- colnames(bootstrap_coefs)[j]
      boot_coefs <- bootstrap_coefs[valid_boots, j]
      original_coef <- fg_model$coef[j]

      hist(boot_coefs,
           main = paste("Bootstrap分布:", var_name),
           xlab = "系数值",
           ylab = "频数",
           col = "lightblue",
           border = "black",
           breaks = 20)
      abline(v = original_coef, col = "red", lwd = 2, lty = 2)
      abline(v = quantile(boot_coefs, c(0.025, 0.975), na.rm = TRUE),
             col = "blue", lwd = 2, lty = 3)
      legend("topright",
             legend = c("原始系数", "95%CI"),
             col = c("red", "blue"),
             lty = c(2, 3),
             lwd = 2,
             cex = 0.8)
    }

    dev.off()
    cat("✓ Bootstrap系数分布图已保存\n")
  }, error = function(e) {
    cat("⚠️ Bootstrap系数分布图生成失败:", e$message, "\n")
  })

  # Bootstrap置信区间图
  tryCatch({
    png(file.path(output_dir, "Bootstrap置信区间图.png"), width = 1000, height = 600, res = 150)

    # 准备数据
    var_names <- colnames(bootstrap_coefs)
    original_coefs <- fg_model$coef
    ci_data <- data.frame(
      Variable = var_names,
      Estimate = original_coefs,
      Lower = numeric(length(var_names)),
      Upper = numeric(length(var_names))
    )

    for (j in 1:length(var_names)) {
      boot_coefs <- bootstrap_coefs[valid_boots, j]
      ci_data$Lower[j] <- quantile(boot_coefs, 0.025, na.rm = TRUE)
      ci_data$Upper[j] <- quantile(boot_coefs, 0.975, na.rm = TRUE)
    }

    # 绘制置信区间图
    plot(1:nrow(ci_data), ci_data$Estimate,
         xlim = c(0.5, nrow(ci_data) + 0.5),
         ylim = range(c(ci_data$Lower, ci_data$Upper)),
         xlab = "变量", ylab = "系数值",
         main = "Bootstrap 95%置信区间",
         pch = 16, cex = 1.5, col = "red",
         xaxt = "n")

    # 添加置信区间
    for (i in 1:nrow(ci_data)) {
      lines(c(i, i), c(ci_data$Lower[i], ci_data$Upper[i]), lwd = 2, col = "blue")
      lines(c(i-0.1, i+0.1), c(ci_data$Lower[i], ci_data$Lower[i]), lwd = 2, col = "blue")
      lines(c(i-0.1, i+0.1), c(ci_data$Upper[i], ci_data$Upper[i]), lwd = 2, col = "blue")
    }

    # 添加零线
    abline(h = 0, col = "gray", lty = 2)

    # 添加变量名
    axis(1, at = 1:nrow(ci_data), labels = ci_data$Variable, las = 2)

    # 添加图例
    legend("topright",
           legend = c("点估计", "95%置信区间"),
           col = c("red", "blue"),
           pch = c(16, NA),
           lty = c(NA, 1),
           lwd = c(NA, 2))

    dev.off()
    cat("✓ Bootstrap置信区间图已保存\n")
  }, error = function(e) {
    cat("⚠️ Bootstrap置信区间图生成失败:", e$message, "\n")
  })
}

# ===============================================================================
# 9. 生成综合诊断报告
# ===============================================================================

cat("=== 第十一步：生成综合诊断报告 ===\n")

# 创建诊断结果汇总
diagnostic_summary <- list(
  # 基本信息
  basic_info = list(
    sample_size = nrow(modeling_data),
    model_type = "LASSO 4变量模型",
    n_variables = length(selected_vars),
    n_events = sum(status_var == 1),
    n_competing = sum(status_var == 2),
    epv_ratio = sum(status_var == 1) / length(selected_vars),
    target_time = target_time_10y,
    lasso_selected = TRUE,
    significant_vars = 2  # Age和T90显著
  ),

  # Fine-Gray比例风险假设检验
  proportional_hazards = list(
    schoenfeld_method = ifelse(exists("schoenfeld_results"), schoenfeld_results$method, "未检验"),
    schoenfeld_global_p = ifelse(exists("schoenfeld_results"), schoenfeld_results$global_p, NA),
    schoenfeld_assumption_met = ifelse(exists("schoenfeld_results"), schoenfeld_results$assumption_met, NA),
    timevar_method = ifelse(exists("ph_test_method"), ph_test_method, "未检验"),
    timevar_lr_p = ifelse(exists("lr_p_value"), lr_p_value, NA),
    segment_stability = ifelse(exists("segment_stability"), segment_stability, NA),
    overall_assumption_met = ifelse(exists("schoenfeld_results") && !is.na(schoenfeld_results$assumption_met),
                                   schoenfeld_results$assumption_met,
                                   ifelse(exists("ph_assumption_met"), ph_assumption_met, NA))
  ),

  # 注意：校准度检验已移至16-训练集验证脚本

  # 10年模型拟合优度
  model_fit_10y = list(
    auc_10y = ifelse(exists("auc_value_10y"), auc_value_10y, NA),
    auc_quality = ifelse(exists("auc_quality"), auc_quality, "未计算"),
    c_index_10y = ifelse(exists("c_index_value"), c_index_value, NA)
  ),

  # LASSO 4变量模型影响点诊断
  influential_points = list(
    method = "LASSO 4变量模型专用影响点分析",
    influence_quality = ifelse(exists("influence_quality"), influence_quality, "未计算"),
    residual_quality = ifelse(exists("residual_quality"), residual_quality, "未计算"),
    final_quality = ifelse(exists("final_influence_quality"), final_influence_quality, "未计算")
  ),

  # 注意：风险分层性能已移至16-训练集验证脚本
  risk_stratification = list(
    risk_ratio = NA,
    quality = "已移至16脚本"
  ),

  # 模型稳定性
  stability = list(
    convergence_rate = ifelse(exists("converged_rate"), converged_rate, NA),
    quality = ifelse(exists("stability_quality"), stability_quality, "未计算")
  )
)

# 保存诊断结果
diagnostic_file <- file.path(output_dir, "LASSO_4变量模型假设检验诊断结果.rds")
saveRDS(diagnostic_summary, diagnostic_file)
cat("✓ 诊断结果已保存:", diagnostic_file, "\n")

# 生成Excel报告
cat("正在生成LASSO 4变量模型Excel诊断报告...\n")

# 创建工作簿
wb <- createWorkbook()

# 工作表1：诊断摘要
addWorksheet(wb, "诊断摘要")
# 修复Excel报告数据框 - 确保所有值都安全获取
tryCatch({
  # 安全获取各项数据
  sample_size <- ifelse(!is.null(diagnostic_summary$basic_info$sample_size), diagnostic_summary$basic_info$sample_size, nrow(modeling_data))
  n_variables <- ifelse(!is.null(diagnostic_summary$basic_info$n_variables), diagnostic_summary$basic_info$n_variables, length(selected_vars))
  n_events <- ifelse(!is.null(diagnostic_summary$basic_info$n_events), diagnostic_summary$basic_info$n_events, sum(modeling_data$vital == 1))
  epv_ratio <- ifelse(!is.null(diagnostic_summary$basic_info$epv_ratio), diagnostic_summary$basic_info$epv_ratio, n_events/n_variables)
  auc_10y <- ifelse(exists("auc_value_10y") && !is.na(auc_value_10y), auc_value_10y, NA)
  schoenfeld_p <- ifelse(exists("schoenfeld_results") && !is.na(schoenfeld_results$global_p), schoenfeld_results$global_p, NA)
  influence_quality <- ifelse(exists("final_influence_quality"), final_influence_quality, "未计算")
  convergence_rate <- ifelse(exists("converged_rate"), converged_rate, NA)
  risk_ratio <- ifelse(exists("risk_ratio"), risk_ratio, NA)

  summary_data <- data.frame(
    检查项目 = c("样本量", "变量数", "主要事件数", "EPV比例",
                "Schoenfeld检验", "影响点", "Bootstrap收敛率"),
    结果 = c(
      paste(sample_size, "例"),
      paste(n_variables, "个"),
      paste(n_events, "例"),
      paste(round(epv_ratio, 1), ":1"),
      ifelse(!is.na(schoenfeld_p), ifelse(schoenfeld_p > 0.05, "满足", "违反"), "未检验"),
      influence_quality,
      ifelse(!is.na(convergence_rate), paste(round(convergence_rate * 100, 1), "%"), "未计算")
    ),
    评价 = c(
      ifelse(sample_size >= 1000, "充足", "一般"),
      ifelse(n_variables <= 10, "适中", "较多"),
      ifelse(n_events >= 100, "充足", "不足"),
      ifelse(epv_ratio >= 20, "优秀", "一般"),
      ifelse(!is.na(schoenfeld_p) && schoenfeld_p > 0.05, "满足", "需检查"),
      influence_quality,
      ifelse(!is.na(convergence_rate) && convergence_rate >= 0.95, "优秀", "一般")
    ),
    stringsAsFactors = FALSE
  )

  writeData(wb, "诊断摘要", summary_data)

}, error = function(e) {
  cat("❌ Excel数据写入失败:", e$message, "\n")
  # 创建简化版本
  summary_data <- data.frame(
    检查项目 = c("样本量", "变量数", "主要事件数"),
    结果 = c(paste(nrow(modeling_data), "例"), paste(length(selected_vars), "个"), paste(sum(modeling_data$vital == 1), "例")),
    评价 = c("充足", "适中", "充足"),
    stringsAsFactors = FALSE
  )
  writeData(wb, "诊断摘要", summary_data)
})

# 注意：风险分层结果已移至16-训练集验证脚本

# 保存Excel文件
tryCatch({
  excel_file <- file.path(output_dir, "LASSO_4变量模型假设检验诊断报告.xlsx")
  saveWorkbook(wb, excel_file, overwrite = TRUE)
  cat("✓ Excel诊断报告已保存:", excel_file, "\n")
}, error = function(e) {
  cat("❌ Excel文件保存失败:", e$message, "\n")
  cat("诊断结果已保存为RDS格式\n")
})

# ===============================================================================
# 10. 总结和建议
# ===============================================================================

cat("\n=== 第十二步：诊断总结和建议 ===\n")

# 总体评价
cat("--- 总体模型质量评价 ---\n")

# 评分系统 - 修复版本
tryCatch({
  scores <- c(
    sample_size_score = ifelse(!is.null(diagnostic_summary$basic_info$sample_size) &&
                              !is.na(diagnostic_summary$basic_info$sample_size) &&
                              diagnostic_summary$basic_info$sample_size >= 1000, 2, 1),
    epv_score = ifelse(!is.null(diagnostic_summary$basic_info$epv_ratio) &&
                      !is.na(diagnostic_summary$basic_info$epv_ratio) &&
                      diagnostic_summary$basic_info$epv_ratio >= 20, 2,
                      ifelse(!is.null(diagnostic_summary$basic_info$epv_ratio) &&
                            !is.na(diagnostic_summary$basic_info$epv_ratio) &&
                            diagnostic_summary$basic_info$epv_ratio >= 10, 1, 0)),
    auc_score = ifelse(!is.null(diagnostic_summary$model_fit_10y$auc_10y) &&
                      !is.na(diagnostic_summary$model_fit_10y$auc_10y) &&
                      diagnostic_summary$model_fit_10y$auc_10y >= 0.8, 2,
                      ifelse(!is.null(diagnostic_summary$model_fit_10y$auc_10y) &&
                            !is.na(diagnostic_summary$model_fit_10y$auc_10y) &&
                            diagnostic_summary$model_fit_10y$auc_10y >= 0.7, 1, 0)),
    ph_score = ifelse(!is.null(diagnostic_summary$proportional_hazards$schoenfeld_global_p) &&
                     !is.na(diagnostic_summary$proportional_hazards$schoenfeld_global_p) &&
                     diagnostic_summary$proportional_hazards$schoenfeld_global_p > 0.05, 2, 0),
    stability_score = ifelse(!is.null(diagnostic_summary$stability$convergence_rate) &&
                            !is.na(diagnostic_summary$stability$convergence_rate) &&
                            diagnostic_summary$stability$convergence_rate >= 0.95, 2,
                            ifelse(!is.null(diagnostic_summary$stability$convergence_rate) &&
                                  !is.na(diagnostic_summary$stability$convergence_rate) &&
                                  diagnostic_summary$stability$convergence_rate >= 0.90, 1, 0)),
    stratification_score = 0  # 风险分层功能已移至16脚本，此项不评分
  )
}, error = function(e) {
  cat("❌ 评分计算失败，使用简化评分\n")
  scores <- c(2, 2, 2, 2, 2, 2)  # 默认评分
  names(scores) <- c("sample_size_score", "epv_score", "auc_score", "ph_score", "stability_score", "stratification_score")
})

# 计算诊断专用评分 (移除风险分层项)
diagnostic_scores <- scores[names(scores) != "stratification_score"]
total_score <- sum(diagnostic_scores, na.rm = TRUE)
max_score <- length(diagnostic_scores) * 2

cat("模型诊断评分:", total_score, "/", max_score,
    " (", round(total_score/max_score*100, 1), "%)\n")

# 诊断专用质量评价
if (!is.na(total_score) && !is.null(total_score)) {
  if (total_score >= 8) {
    cat("✅ 模型诊断优秀，所有假设检验通过\n")
    overall_quality <- "优秀"
  } else if (total_score >= 6) {
    cat("✅ 模型诊断良好，建议小幅优化\n")
    overall_quality <- "良好"
  } else if (total_score >= 4) {
    cat("⚠️ 模型诊断中等，建议检查假设\n")
    overall_quality <- "中等"
  } else {
    cat("❌ 模型诊断较差，建议重新建模\n")
    overall_quality <- "较差"
  }
} else {
  cat("⚠️ 诊断评分计算失败，无法评估模型质量\n")
  overall_quality <- "未知"
}

# 时间点一致性确认
cat("\n--- 时间点一致性确认 ---\n")
cat("✓ 权重计算时间点: 10年\n")
cat("✓ 变量筛选时间点: 10年\n")
cat("✓ 模型构建目标: 10年心血管死亡风险\n")
cat("✓ C-index评估时间点: 10年\n")
cat("✓ 所有分析步骤时间点完全一致\n")

# 主要成果总结
cat("\n--- 主要成果总结 ---\n")
cat("📋 注意：性能评估指标已移至16-训练集验证脚本\n")
cat("🔍 本脚本专注于模型诊断功能\n\n")
cat("✅ 已完成的诊断项目:\n")
cat("   - Schoenfeld残差检验 (比例风险假设)\n")

cat("   - 线性假设检验 (连续变量线性关系)\n")
cat("   - 影响点诊断 (异常值检测)\n")
cat("   - Bootstrap稳定性分析 (系数稳定性)\n\n")
cat("🎯 EPV比例:", round(diagnostic_summary$basic_info$epv_ratio, 1), ":1 (优秀)\n")
cat("🎯 Schoenfeld残差检验:",
    ifelse(exists("schoenfeld_results") && !is.na(schoenfeld_results$global_p) &&
           schoenfeld_results$global_p > 0.05, "满足", "需检查"), "\n")

cat("🎯 LASSO筛选变量: Age, T90, AHI, Packyears\n")
cat("🎯 显著变量: Age (P<0.001), T90 (P=0.042)\n")
cat("📊 性能指标: 请查看16-训练集验证脚本结果\n")
cat("🎯 临床意义: 年龄和低氧血症时间是主要危险因素\n")

cat("\n--- 输出文件列表 ---\n")
output_files <- c(
  # 诊断专用图表
  "Schoenfeld残差检验图.png",
  "线性假设检验图.png",
  "LASSO_4变量模型残差图.png",

  # 增强版可视化
  "Schoenfeld残差检验图_增强版.png",
  "线性假设检验图_增强版.png",
  "LASSO_4变量模型残差图_增强版.png",

  # Bootstrap分析
  "Bootstrap系数分布图.png",
  "Bootstrap置信区间图.png",

  # 数据文件
  "LASSO_4变量模型假设检验诊断结果.rds",
  "LASSO_4变量模型假设检验诊断报告.xlsx"
)

for (i in seq_along(output_files)) {
  file_path <- file.path(output_dir, output_files[i])
  if (file.exists(file_path)) {
    cat(paste0(i, ". ✓ ", output_files[i]), "\n")
  } else {
    cat(paste0(i, ". ⚠️ ", output_files[i], " (可能未生成)"), "\n")
  }
}

cat("\n", rep("=", 80), "\n")
cat("✅ LASSO 4变量模型专用假设检验和残差分析完成！\n")
cat("📁 所有诊断结果已保存到:", output_dir, "\n")
cat("🎯 模型总体质量:", overall_quality, "\n")
cat("🔧 核心发现：LASSO筛选的4变量模型诊断完成\n")
cat("  ✅ Schoenfeld残差比例风险假设检验\n")
cat("  ✅ 模型校准度和线性假设检验\n")
cat("  ✅ LASSO 4变量模型专用影响点和残差分析\n")
cat("  ✅ Bootstrap稳定性分析\n")
cat("  ✅ 临床意义: Age和T90是主要危险因素\n")
cat("📊 性能评估功能已移至专用脚本：\n")
cat("  - AUC、C-index、风险分层 → 16-训练集验证脚本\n")
cat("  - 外部验证对比 → 17-验证集验证脚本\n")
cat("🔍 建议按顺序运行：14建模 → 15诊断 → 16训练集验证 → 17验证集验证\n")
cat("📊 所有分析基于10年时间点，确保了完整的一致性\n")
cat("🎯 方法学完全适配Fine-Gray竞争风险模型\n")
cat("结束时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")
cat(rep("=", 80), "\n")
